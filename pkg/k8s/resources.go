/*
Copyright 2025 ETCD Operator Team.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package k8s

import (
	"fmt"
	"strings"

	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/intstr"

	etcdv1alpha1 "github.com/your-org/etcd-k8s-operator/api/v1alpha1"
	"github.com/your-org/etcd-k8s-operator/pkg/utils"
)

// BuildStatefulSet creates a StatefulSet for the EtcdCluster
func BuildStatefulSet(cluster *etcdv1alpha1.EtcdCluster) *appsv1.StatefulSet {
	return BuildStatefulSetWithReplicas(cluster, cluster.Spec.Size)
}

// BuildStatefulSetWithReplicas creates a StatefulSet with specified replica count
func BuildStatefulSetWithReplicas(cluster *etcdv1alpha1.EtcdCluster, replicas int32) *appsv1.StatefulSet {
	labels := utils.LabelsForEtcdCluster(cluster)
	selectorLabels := utils.SelectorLabelsForEtcdCluster(cluster)

	sts := &appsv1.StatefulSet{
		ObjectMeta: metav1.ObjectMeta{
			Name:        cluster.Name,
			Namespace:   cluster.Namespace,
			Labels:      labels,
			Annotations: utils.AnnotationsForEtcdCluster(cluster),
		},
		Spec: appsv1.StatefulSetSpec{
			Replicas:    &replicas,
			ServiceName: fmt.Sprintf("%s-peer", cluster.Name),
			Selector: &metav1.LabelSelector{
				MatchLabels: selectorLabels,
			},
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels:      labels,
					Annotations: utils.AnnotationsForEtcdCluster(cluster),
				},
				Spec: buildPodSpec(cluster),
			},
			VolumeClaimTemplates: buildVolumeClaimTemplates(cluster),
			PodManagementPolicy:  appsv1.ParallelPodManagement,
			UpdateStrategy: appsv1.StatefulSetUpdateStrategy{
				Type: appsv1.RollingUpdateStatefulSetStrategyType,
			},
		},
	}

	return sts
}

// buildPodSpec creates the pod specification for etcd
func buildPodSpec(cluster *etcdv1alpha1.EtcdCluster) corev1.PodSpec {
	// Build init containers for multi-node setup
	var initContainers []corev1.Container
	if cluster.Spec.Size > 1 {
		initContainers = append(initContainers, buildEtcdInitContainer(cluster))
	}

	containers := []corev1.Container{
		buildEtcdContainer(cluster),
	}

	// Add netshoot sidecar container for debugging (always available for troubleshooting)
	netshootContainer := corev1.Container{
		Name:    "netshoot",
		Image:   "nicolaka/netshoot:latest",
		Command: []string{"sleep", "3600"},
		Resources: corev1.ResourceRequirements{
			Requests: corev1.ResourceList{
				corev1.ResourceCPU:    resource.MustParse("50m"),
				corev1.ResourceMemory: resource.MustParse("64Mi"),
			},
			Limits: corev1.ResourceList{
				corev1.ResourceCPU:    resource.MustParse("100m"),
				corev1.ResourceMemory: resource.MustParse("128Mi"),
			},
		},
	}
	containers = append(containers, netshootContainer)

	// Build volumes for multi-node setup
	var volumes []corev1.Volume
	if cluster.Spec.Size > 1 {
		volumes = append(volumes, corev1.Volume{
			Name: "shared-config",
			VolumeSource: corev1.VolumeSource{
				EmptyDir: &corev1.EmptyDirVolumeSource{},
			},
		})
	}

	return corev1.PodSpec{
		InitContainers:                initContainers,
		Containers:                    containers,
		Volumes:                       volumes,
		RestartPolicy:                 corev1.RestartPolicyAlways,
		TerminationGracePeriodSeconds: &[]int64{30}[0],
		DNSPolicy:                     corev1.DNSClusterFirst,
		SecurityContext: &corev1.PodSecurityContext{
			FSGroup: &[]int64{1000}[0],
		},
	}
}

// buildEtcdContainer creates the etcd container specification
func buildEtcdContainer(cluster *etcdv1alpha1.EtcdCluster) corev1.Container {
	image := fmt.Sprintf("%s:%s", cluster.Spec.Repository, cluster.Spec.Version)

	container := corev1.Container{
		Name:  "etcd",
		Image: image,
		Ports: []corev1.ContainerPort{
			{
				Name:          "client",
				ContainerPort: utils.EtcdClientPort,
				Protocol:      corev1.ProtocolTCP,
			},
			{
				Name:          "peer",
				ContainerPort: utils.EtcdPeerPort,
				Protocol:      corev1.ProtocolTCP,
			},
		},
		Env:            buildEtcdEnvironment(cluster),
		VolumeMounts:   buildVolumeMounts(cluster),
		LivenessProbe:  buildLivenessProbe(cluster),
		ReadinessProbe: buildReadinessProbe(cluster),
		Resources:      buildResourceRequirements(cluster),
	}

	return container
}

// buildVolumeMounts creates volume mounts for etcd container
func buildVolumeMounts(cluster *etcdv1alpha1.EtcdCluster) []corev1.VolumeMount {
	mounts := []corev1.VolumeMount{
		{
			Name:      "data",
			MountPath: utils.EtcdDataDir,
		},
	}

	// Add shared config mount for multi-node clusters
	if cluster.Spec.Size > 1 {
		mounts = append(mounts, corev1.VolumeMount{
			Name:      "shared-config",
			MountPath: "/shared",
			ReadOnly:  true,
		})
	}

	return mounts
}

// buildEtcdEnvironment creates environment variables for etcd
func buildEtcdEnvironment(cluster *etcdv1alpha1.EtcdCluster) []corev1.EnvVar {
	// 基础环境变量 - 适用于官方镜像和 Bitnami 镜像
	envVars := []corev1.EnvVar{
		{
			Name: "ETCD_NAME",
			ValueFrom: &corev1.EnvVarSource{
				FieldRef: &corev1.ObjectFieldSelector{
					FieldPath: "metadata.name",
				},
			},
		},
		{
			Name:  "ETCD_DATA_DIR",
			Value: utils.EtcdDataDir,
		},
		{
			Name:  "ETCD_LISTEN_CLIENT_URLS",
			Value: fmt.Sprintf("http://0.0.0.0:%d", utils.EtcdClientPort),
		},
		{
			Name:  "ETCD_LISTEN_PEER_URLS",
			Value: fmt.Sprintf("http://0.0.0.0:%d", utils.EtcdPeerPort),
		},
		{
			Name:  "ETCD_ADVERTISE_CLIENT_URLS",
			Value: fmt.Sprintf("http://$(ETCD_NAME).%s-peer.%s.svc.cluster.local:%d", cluster.Name, cluster.Namespace, utils.EtcdClientPort),
		},
		{
			Name:  "ETCD_INITIAL_ADVERTISE_PEER_URLS",
			Value: fmt.Sprintf("http://$(ETCD_NAME).%s-peer.%s.svc.cluster.local:%d", cluster.Name, cluster.Namespace, utils.EtcdPeerPort),
		},
		{
			Name:  "ETCD_INITIAL_CLUSTER_STATE",
			Value: "new", // 对于新集群，所有节点都使用 "new"
		},
		{
			Name:  "ETCD_INITIAL_CLUSTER_TOKEN",
			Value: cluster.Name,
		},
	}

	// 对于多节点集群，第一个节点以单节点模式启动，后续节点通过动态扩容添加
	if cluster.Spec.Size > 1 {
		// 第一个节点使用单节点配置，后续节点将通过 etcd member add 动态加入
		envVars = append(envVars, corev1.EnvVar{
			Name:  "ETCD_INITIAL_CLUSTER",
			Value: buildDynamicInitialCluster(cluster),
		})
	} else {
		// 单节点集群使用简单配置
		envVars = append(envVars, corev1.EnvVar{
			Name:  "ETCD_INITIAL_CLUSTER",
			Value: buildInitialCluster(cluster),
		})
	}

	// 添加镜像特定的环境变量
	if strings.Contains(cluster.Spec.Repository, "bitnami") {
		// Bitnami 镜像特定配置（保持向后兼容）
		bitnamiEnvVars := []corev1.EnvVar{
			{
				Name:  "ALLOW_NONE_AUTHENTICATION",
				Value: "yes",
			},
			{
				Name:  "ETCD_ROOT_PASSWORD",
				Value: "",
			},
			{
				Name:  "MY_STS_NAME",
				Value: cluster.Name,
			},
			{
				Name:  "ETCD_ON_K8S",
				Value: "yes",
			},
			{
				Name:  "ETCD_CLUSTER_DOMAIN",
				Value: fmt.Sprintf("%s-peer.%s.svc.cluster.local", cluster.Name, cluster.Namespace),
			},
		}
		envVars = append(envVars, bitnamiEnvVars...)
	} else {
		// 官方镜像配置 - 使用标准 etcd 环境变量
		// 官方镜像不需要额外的环境变量，使用标准配置即可
	}

	return envVars
}

// buildLivenessProbe creates liveness probe for etcd container
func buildLivenessProbe(cluster *etcdv1alpha1.EtcdCluster) *corev1.Probe {
	if strings.Contains(cluster.Spec.Repository, "bitnami") {
		// Use Bitnami's healthcheck script
		return &corev1.Probe{
			ProbeHandler: corev1.ProbeHandler{
				Exec: &corev1.ExecAction{
					Command: []string{"/opt/bitnami/scripts/etcd/healthcheck.sh"},
				},
			},
			InitialDelaySeconds: 60, // Bitnami etcd takes longer to start
			PeriodSeconds:       10,
			TimeoutSeconds:      5,
			FailureThreshold:    3,
		}
	}

	// 官方镜像使用 etcdctl 进行健康检查
	return &corev1.Probe{
		ProbeHandler: corev1.ProbeHandler{
			Exec: &corev1.ExecAction{
				Command: []string{
					"etcdctl",
					"--endpoints=http://localhost:2379",
					"endpoint",
					"health",
				},
			},
		},
		InitialDelaySeconds: 30,
		PeriodSeconds:       10,
		TimeoutSeconds:      5,
		FailureThreshold:    3,
	}
}

// buildReadinessProbe creates readiness probe for etcd container
func buildReadinessProbe(cluster *etcdv1alpha1.EtcdCluster) *corev1.Probe {
	if strings.Contains(cluster.Spec.Repository, "bitnami") {
		// For multi-node clusters, use a more lenient readiness probe
		if cluster.Spec.Size > 1 {
			// Use TCP probe to allow Pod to become ready faster
			// This helps with DNS record creation for headless service
			return &corev1.Probe{
				ProbeHandler: corev1.ProbeHandler{
					TCPSocket: &corev1.TCPSocketAction{
						Port: intstr.FromInt(utils.EtcdClientPort),
					},
				},
				InitialDelaySeconds: 15, // Shorter delay for multi-node
				PeriodSeconds:       5,
				TimeoutSeconds:      3,
				FailureThreshold:    5, // More tolerant for multi-node startup
			}
		}

		// Use Bitnami's healthcheck script for single-node clusters
		return &corev1.Probe{
			ProbeHandler: corev1.ProbeHandler{
				Exec: &corev1.ExecAction{
					Command: []string{"/opt/bitnami/scripts/etcd/healthcheck.sh"},
				},
			},
			InitialDelaySeconds: 30, // Shorter delay for readiness
			PeriodSeconds:       5,
			TimeoutSeconds:      3,
			FailureThreshold:    3,
		}
	}

	// 官方镜像使用 etcdctl 进行就绪检查
	return &corev1.Probe{
		ProbeHandler: corev1.ProbeHandler{
			Exec: &corev1.ExecAction{
				Command: []string{
					"etcdctl",
					"--endpoints=http://localhost:2379",
					"endpoint",
					"health",
				},
			},
		},
		InitialDelaySeconds: 15, // 官方镜像启动较快
		PeriodSeconds:       5,
		TimeoutSeconds:      3,
		FailureThreshold:    3,
	}
}

// buildInitialCluster creates the initial cluster configuration
func buildInitialCluster(cluster *etcdv1alpha1.EtcdCluster) string {
	var members []string
	for i := int32(0); i < cluster.Spec.Size; i++ {
		memberName := fmt.Sprintf("%s-%d", cluster.Name, i)
		memberURL := fmt.Sprintf("http://%s.%s-peer.%s.svc.cluster.local:%d",
			memberName, cluster.Name, cluster.Namespace, utils.EtcdPeerPort)
		members = append(members, fmt.Sprintf("%s=%s", memberName, memberURL))
	}
	return strings.Join(members, ",")
}

// buildDynamicInitialCluster creates initial cluster configuration for dynamic multi-node setup
// For multi-node clusters, the first node starts as a single-node cluster
// Other nodes will be added dynamically through etcd member management API
func buildDynamicInitialCluster(cluster *etcdv1alpha1.EtcdCluster) string {
	// 对于多节点集群，第一个节点以单节点模式启动
	// 这样避免了等待其他节点的问题
	firstNodeName := fmt.Sprintf("%s-0", cluster.Name)
	firstNodeURL := fmt.Sprintf("http://%s.%s-peer.%s.svc.cluster.local:%d",
		firstNodeName, cluster.Name, cluster.Namespace, utils.EtcdPeerPort)

	// 只返回第一个节点的配置，其他节点将通过动态扩容添加
	return fmt.Sprintf("%s=%s", firstNodeName, firstNodeURL)
}

// buildEtcdInitContainer creates an init container for multi-node etcd setup
func buildEtcdInitContainer(cluster *etcdv1alpha1.EtcdCluster) corev1.Container {
	script := `#!/bin/sh
set -e

# 获取当前节点信息
HOSTNAME=$(hostname)
FIRST_NODE="` + cluster.Name + `-0"

echo "Current hostname: $HOSTNAME"
echo "First node: $FIRST_NODE"

# 如果是第一个节点，直接退出（使用默认配置）
if [ "$HOSTNAME" = "$FIRST_NODE" ]; then
    echo "This is the first node, using single-node configuration"
    exit 0
fi

echo "This is a joining node, configuring for existing cluster"

# 等待第一个节点就绪
FIRST_NODE_URL="http://$FIRST_NODE.` + cluster.Name + `-peer.` + cluster.Namespace + `.svc.cluster.local:2379"
echo "Waiting for first node to be ready: $FIRST_NODE_URL"

# 等待第一个节点可用 - 检查 etcd API 而不是健康检查
for i in $(seq 1 60); do
    if wget -q --spider "$FIRST_NODE_URL/version" 2>/dev/null; then
        echo "First node is ready"
        break
    fi
    echo "Waiting for first node... ($i/60)"
    sleep 5
done

# 设置环境变量文件，供主容器使用
cat > /shared/etcd-config.env << EOF
ETCD_INITIAL_CLUSTER_STATE=existing
ETCD_INITIAL_CLUSTER=` + buildInitialCluster(cluster) + `
EOF

echo "Configuration written to /shared/etcd-config.env"
`

	return corev1.Container{
		Name:    "etcd-init",
		Image:   "busybox:1.35",
		Command: []string{"/bin/sh", "-c", script},
		VolumeMounts: []corev1.VolumeMount{
			{
				Name:      "shared-config",
				MountPath: "/shared",
			},
		},
		Resources: corev1.ResourceRequirements{
			Requests: corev1.ResourceList{
				corev1.ResourceCPU:    resource.MustParse("50m"),
				corev1.ResourceMemory: resource.MustParse("32Mi"),
			},
			Limits: corev1.ResourceList{
				corev1.ResourceCPU:    resource.MustParse("100m"),
				corev1.ResourceMemory: resource.MustParse("64Mi"),
			},
		},
	}
}

// buildResourceRequirements creates resource requirements for etcd container
func buildResourceRequirements(cluster *etcdv1alpha1.EtcdCluster) corev1.ResourceRequirements {
	// Use cluster-specific resources if provided, otherwise use defaults
	if cluster.Spec.Resources.Requests != nil || cluster.Spec.Resources.Limits != nil {
		return corev1.ResourceRequirements{
			Requests: cluster.Spec.Resources.Requests,
			Limits:   cluster.Spec.Resources.Limits,
		}
	}

	// Default resource requirements
	return corev1.ResourceRequirements{
		Requests: corev1.ResourceList{
			corev1.ResourceCPU:    resource.MustParse("100m"),
			corev1.ResourceMemory: resource.MustParse("128Mi"),
		},
		Limits: corev1.ResourceList{
			corev1.ResourceCPU:    resource.MustParse("1000m"),
			corev1.ResourceMemory: resource.MustParse("1Gi"),
		},
	}
}

// buildVolumeClaimTemplates creates volume claim templates for StatefulSet
func buildVolumeClaimTemplates(cluster *etcdv1alpha1.EtcdCluster) []corev1.PersistentVolumeClaim {
	storageSize := cluster.Spec.Storage.Size
	if storageSize.IsZero() {
		storageSize = resource.MustParse(utils.DefaultStorageSize)
	}

	pvc := corev1.PersistentVolumeClaim{
		ObjectMeta: metav1.ObjectMeta{
			Name:   "data",
			Labels: utils.LabelsForEtcdCluster(cluster),
		},
		Spec: corev1.PersistentVolumeClaimSpec{
			AccessModes: []corev1.PersistentVolumeAccessMode{
				corev1.ReadWriteOnce,
			},
			Resources: corev1.VolumeResourceRequirements{
				Requests: corev1.ResourceList{
					corev1.ResourceStorage: storageSize,
				},
			},
		},
	}

	// Set storage class if specified
	if cluster.Spec.Storage.StorageClassName != nil {
		pvc.Spec.StorageClassName = cluster.Spec.Storage.StorageClassName
	}

	return []corev1.PersistentVolumeClaim{pvc}
}

// BuildClientService creates a client service for the EtcdCluster
func BuildClientService(cluster *etcdv1alpha1.EtcdCluster) *corev1.Service {
	labels := utils.LabelsForEtcdService(cluster, "client")
	selectorLabels := utils.SelectorLabelsForEtcdCluster(cluster)

	return &corev1.Service{
		ObjectMeta: metav1.ObjectMeta{
			Name:        fmt.Sprintf("%s-client", cluster.Name),
			Namespace:   cluster.Namespace,
			Labels:      labels,
			Annotations: utils.AnnotationsForEtcdCluster(cluster),
		},
		Spec: corev1.ServiceSpec{
			Type:     corev1.ServiceTypeClusterIP,
			Selector: selectorLabels,
			Ports: []corev1.ServicePort{
				{
					Name:       "client",
					Port:       utils.EtcdClientPort,
					TargetPort: intstr.FromInt(utils.EtcdClientPort),
					Protocol:   corev1.ProtocolTCP,
				},
			},
		},
	}
}

// BuildPeerService creates a peer service for the EtcdCluster
func BuildPeerService(cluster *etcdv1alpha1.EtcdCluster) *corev1.Service {
	labels := utils.LabelsForEtcdService(cluster, "peer")
	selectorLabels := utils.SelectorLabelsForEtcdCluster(cluster)

	return &corev1.Service{
		ObjectMeta: metav1.ObjectMeta{
			Name:        fmt.Sprintf("%s-peer", cluster.Name),
			Namespace:   cluster.Namespace,
			Labels:      labels,
			Annotations: utils.AnnotationsForEtcdCluster(cluster),
		},
		Spec: corev1.ServiceSpec{
			Type:      corev1.ServiceTypeClusterIP,
			ClusterIP: corev1.ClusterIPNone, // Headless service
			Selector:  selectorLabels,
			Ports: []corev1.ServicePort{
				{
					Name:       "peer",
					Port:       utils.EtcdPeerPort,
					TargetPort: intstr.FromInt(utils.EtcdPeerPort),
					Protocol:   corev1.ProtocolTCP,
				},
			},
		},
	}
}

// BuildConfigMap creates a ConfigMap for etcd configuration
func BuildConfigMap(cluster *etcdv1alpha1.EtcdCluster) *corev1.ConfigMap {
	labels := utils.LabelsForEtcdCluster(cluster)

	return &corev1.ConfigMap{
		ObjectMeta: metav1.ObjectMeta{
			Name:        fmt.Sprintf("%s-config", cluster.Name),
			Namespace:   cluster.Namespace,
			Labels:      labels,
			Annotations: utils.AnnotationsForEtcdCluster(cluster),
		},
		Data: map[string]string{
			"etcd.conf": buildEtcdConfig(cluster),
		},
	}
}

// buildEtcdConfig creates etcd configuration content
func buildEtcdConfig(cluster *etcdv1alpha1.EtcdCluster) string {
	config := fmt.Sprintf(`# etcd configuration for cluster %s
name: $(ETCD_NAME)
data-dir: %s
listen-client-urls: http://0.0.0.0:%d
listen-peer-urls: http://0.0.0.0:%d
advertise-client-urls: http://$(ETCD_NAME).%s-peer.%s.svc.cluster.local:%d
initial-advertise-peer-urls: http://$(ETCD_NAME).%s-peer.%s.svc.cluster.local:%d
initial-cluster-state: new
initial-cluster-token: %s
initial-cluster: %s
`,
		cluster.Name,
		utils.EtcdDataDir,
		utils.EtcdClientPort,
		utils.EtcdPeerPort,
		cluster.Name, cluster.Namespace, utils.EtcdClientPort,
		cluster.Name, cluster.Namespace, utils.EtcdPeerPort,
		cluster.Name,
		buildInitialCluster(cluster),
	)

	return config
}
